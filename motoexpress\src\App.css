/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.app {
  min-height: 100vh;
}

/* Loading Spinner */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Auth Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.auth-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

.logo {
  text-align: center;
  margin-bottom: 2rem;
}

.logo h1 {
  color: #667eea;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.logo p {
  color: #666;
  font-size: 0.9rem;
}

.auth-form h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.form-group small {
  color: #666;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

.info-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  width: 100%;
  margin: 1rem 0;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-success {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-success:hover {
  background: #218838;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-danger:hover {
  background: #c82333;
}

.auth-links {
  text-align: center;
  margin-top: 1rem;
}

.auth-links a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.auth-links a:hover {
  text-decoration: underline;
}

/* Error Messages */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

/* Dashboard Styles */
.dashboard {
  min-height: 100vh;
  background: #f8f9fa;
}

.dashboard-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dashboard-header h1 {
  color: #667eea;
  margin: 0;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-actions span {
  color: #666;
  font-weight: 500;
}

.dashboard-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Status Card (Driver) */
.status-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.status-card h2 {
  margin-bottom: 1rem;
  color: #333;
}

.status-indicator {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.status-indicator.active {
  background: #d4edda;
  color: #155724;
}

.status-indicator.inactive {
  background: #f8d7da;
  color: #721c24;
}

.balance-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.balance-item strong {
  color: #667eea;
  font-size: 1.2rem;
}

.recharge-section {
  border-top: 1px solid #e9ecef;
  padding-top: 1.5rem;
}

.warning {
  background: #fff3cd;
  color: #856404;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #ffeaa7;
}

/* Trip Requests */
.trip-requests {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.trip-requests h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.inactive-message,
.no-trips {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.trip-list {
  display: grid;
  gap: 1rem;
}

.trip-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  background: #f8f9fa;
  transition: transform 0.2s, box-shadow 0.2s;
}

.trip-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.trip-info {
  margin-bottom: 1rem;
}

.trip-route p {
  margin-bottom: 0.5rem;
  color: #555;
}

.trip-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #28a745;
}

.price-increases {
  font-size: 0.8rem;
  color: #666;
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.trip-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.counter-offer {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.counter-offer input {
  width: 100px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Trip Request Form (Passenger) */
.trip-request-form {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.trip-request-form h2 {
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

/* Active Trip (Passenger) */
.active-trip {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.active-trip h2 {
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.route-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.route-info p {
  margin-bottom: 0.5rem;
  color: #555;
}

.price-section {
  text-align: center;
  margin-bottom: 2rem;
}

.current-price {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.price-actions {
  margin-top: 1rem;
}

.price-actions p {
  margin-bottom: 0.5rem;
  color: #666;
}

.max-increases {
  color: #856404;
  background: #fff3cd;
  padding: 0.5rem;
  border-radius: 4px;
  margin-top: 1rem;
}

/* Counter Offers */
.counter-offers {
  margin: 2rem 0;
}

.counter-offers h3 {
  margin-bottom: 1rem;
  color: #333;
}

.counter-offer-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.offer-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.offer-price {
  font-size: 1.2rem;
  font-weight: bold;
  color: #28a745;
}

.offer-driver {
  color: #666;
}

.trip-actions {
  text-align: center;
  margin: 2rem 0;
}

.waiting-message {
  text-align: center;
  padding: 2rem;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-header h1 {
    font-size: 1.2rem;
  }

  .dashboard-content {
    padding: 1rem;
  }

  .balance-info {
    grid-template-columns: 1fr;
  }

  .trip-details {
    flex-direction: column;
    align-items: flex-start;
  }

  .trip-actions {
    width: 100%;
  }

  .trip-actions button {
    flex: 1;
  }

  .counter-offer {
    width: 100%;
  }

  .counter-offer input {
    flex: 1;
  }

  .counter-offer-card {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .auth-card {
    margin: 1rem;
    padding: 1.5rem;
  }
}

/* Mobile-Specific Styles */
.mobile-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.mobile-header {
  flex-shrink: 0;
  padding: 1rem;
}

.mobile-header h1 {
  font-size: 1.2rem;
}

/* Mode Switcher */
.mode-switcher {
  display: flex;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.mode-btn {
  flex: 1;
  padding: 1rem;
  border: none;
  background: white;
  color: #666;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 4px solid transparent;
}

.mode-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
}

.mode-btn:hover {
  background: #f8f9fa;
  color: #667eea;
}

.mobile-nav {
  display: flex;
  background: white;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.nav-btn {
  flex: 1;
  padding: 1rem 0.5rem;
  border: none;
  background: white;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.nav-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9fa;
}

.nav-btn:hover {
  background: #f8f9fa;
  color: #667eea;
}

.mobile-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Active Users Styles */
.active-users {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.active-users-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.location-status {
  background: #e8f5e8;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.9rem;
  color: #155724;
}

.active-users h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
}

.no-users {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.users-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.user-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.user-info {
  flex: 1;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.user-header h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.price-badge {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.location {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.phone {
  color: #666;
  font-size: 0.8rem;
}

.trip-route {
  margin: 0.5rem 0;
}

.trip-route p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #555;
}

.trip-details {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.payment-method {
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  color: #666;
}

.price-increases {
  background: #fff3cd;
  color: #856404;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
}

.standard-price-badge {
  background: #e3f2fd;
  color: #0d47a1;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
}

.price-increase-badge {
  background: #fff3cd;
  color: #856404;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
}

.select-btn {
  color: #667eea;
  font-size: 0.9rem;
  font-weight: 500;
  margin-left: 1rem;
}

.refresh-btn {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: #e9ecef;
  color: #333;
}

/* Selected Driver Styles */
.selected-driver {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.selected-driver h3 {
  margin-bottom: 0.5rem;
  color: #155724;
  font-size: 1rem;
}

.driver-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.driver-info p {
  margin: 0.25rem 0;
  color: #155724;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .mobile-header {
    padding: 0.75rem;
  }

  .mobile-content {
    padding: 0.75rem;
  }

  .nav-btn {
    padding: 0.75rem 0.25rem;
    font-size: 0.8rem;
  }

  .user-card {
    padding: 0.75rem;
  }

  .user-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .select-btn {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .driver-info {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Trip Calculation Styles */
.trip-calculation {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1rem;
  margin: 1rem 0;
}

.distance-info {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: #e8f5e8;
  border-radius: 8px;
  color: #155724;
}

.price-calculator {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.standard-price {
  text-align: center;
  padding: 0.5rem;
  background: #e3f2fd;
  border-radius: 8px;
  color: #0d47a1;
}

.price-increase-section {
  text-align: center;
}

.price-increase-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.price-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.price-btn {
  padding: 0.75rem 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.price-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.price-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
  transform: scale(1.05);
}

.final-price {
  text-align: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: bold;
}

/* Mobile optimizations for price calculator */
@media (max-width: 480px) {
  .price-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .price-btn {
    padding: 1rem 0.5rem;
    font-size: 1rem;
  }

  .trip-calculation {
    margin: 0.75rem 0;
    padding: 0.75rem;
  }
}

/* Photo Verification Styles */
.photo-verification-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  text-align: center;
}

.photo-verification-section h4 {
  margin-bottom: 0.5rem;
  color: #856404;
}

.photo-verification-section p {
  margin-bottom: 1rem;
  color: #856404;
  font-size: 0.9rem;
}

.photo-verification-pending {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.photo-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
}

.photo-preview-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.photo-preview-small img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #667eea;
}

.photo-actions {
  display: flex;
  gap: 0.5rem;
}

.verification-warning {
  color: #dc3545;
  font-weight: bold;
  font-size: 0.9rem;
}

.photo-verification-complete {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.verification-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #155724;
  font-weight: bold;
}

.success-icon {
  font-size: 1.5rem;
}

.small-btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.btn-primary.disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Camera Capture Overlay */
.camera-capture-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-capture-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.camera-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.camera-header h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.camera-header p {
  color: #666;
  font-size: 0.9rem;
}

.camera-preview {
  position: relative;
  margin-bottom: 1rem;
}

.camera-video {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 12px;
  background: #000;
}

.camera-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.camera-instructions {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  text-align: left;
}

.camera-instructions p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #555;
}

.camera-controls {
  display: flex;
  gap: 1rem;
}

.capture-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
}

/* Photo Confirmation Overlay */
.photo-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-confirmation-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.photo-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.photo-header h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.photo-header p {
  color: #666;
  font-size: 0.9rem;
}

.photo-preview {
  margin-bottom: 1.5rem;
}

.captured-photo {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #667eea;
}

.photo-quality-check {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  text-align: left;
}

.photo-quality-check ul {
  margin: 0.5rem 0;
  padding-left: 1rem;
}

.photo-quality-check li {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #555;
}

.photo-controls {
  display: flex;
  gap: 1rem;
}

.security-notice {
  margin-top: 1rem;
  text-align: center;
  color: #666;
}

/* Mobile optimizations for camera */
@media (max-width: 480px) {
  .camera-capture-container,
  .photo-confirmation-container {
    padding: 1rem;
    margin: 1rem;
  }

  .camera-video {
    max-width: 100%;
  }

  .captured-photo {
    width: 150px;
    height: 150px;
  }

  .camera-controls,
  .photo-controls {
    flex-direction: column;
    width: 100%;
  }

  .camera-controls button,
  .photo-controls button {
    width: 100%;
  }
}

/* Rating System Styles */
.btn-rating {
  background: #ffc107;
  color: #212529;
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-rating:hover {
  background: #e0a800;
  transform: scale(1.1);
}

.rating-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rating-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 500px;
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.rating-header h2 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #f8f9fa;
}

.no-pending-ratings {
  text-align: center;
  padding: 2rem;
}

.pending-trips {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.trip-rating-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  background: #f8f9fa;
}

.trip-info {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.trip-info h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.trip-info p {
  margin: 0.25rem 0;
  color: #666;
}

.rating-section h5 {
  margin-bottom: 1rem;
  color: #333;
}

.star-rating {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.star {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  transition: transform 0.2s;
  filter: grayscale(100%);
}

.star:hover {
  transform: scale(1.2);
}

.star.active {
  filter: grayscale(0%);
}

.star.filled {
  filter: grayscale(0%);
}

.rating-labels {
  margin-bottom: 1rem;
  min-height: 1.5rem;
}

.rating-label {
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.9rem;
}

.rating-label.bad { background: #f8d7da; color: #721c24; }
.rating-label.poor { background: #fff3cd; color: #856404; }
.rating-label.average { background: #d1ecf1; color: #0c5460; }
.rating-label.good { background: #d4edda; color: #155724; }
.rating-label.excellent { background: #e2e3e5; color: #383d41; }

.comment-section {
  margin-bottom: 1.5rem;
}

.comment-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.comment-section textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.comment-section small {
  color: #666;
  font-size: 0.8rem;
}

.submit-rating-btn {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: bold;
}

/* Driver Rating Display */
.driver-rating {
  margin: 0.5rem 0;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.rating-stars {
  display: flex;
  gap: 0.1rem;
}

.rating-stars .star {
  font-size: 0.9rem;
  filter: grayscale(100%);
}

.rating-stars .star.filled {
  filter: grayscale(0%);
}

.rating-text {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.no-rating {
  font-size: 0.8rem;
  color: #856404;
  background: #fff3cd;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.trips-count {
  font-size: 0.8rem;
  color: #666;
  margin: 0.25rem 0;
}

/* User Ratings Component */
.user-ratings {
  margin-top: 1rem;
}

.ratings-header {
  margin-bottom: 1.5rem;
}

.ratings-header h3 {
  margin-bottom: 1rem;
  color: #333;
}

.rating-summary {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.average-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.rating-number {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
}

.total-ratings {
  color: #666;
  font-size: 0.9rem;
}

.ratings-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rating-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  background: white;
}

.rating-item .rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.rater-name {
  font-weight: 500;
  color: #333;
}

.rating-date {
  font-size: 0.8rem;
  color: #666;
}

.rating-comment {
  font-style: italic;
  color: #555;
  margin: 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
}

/* Mobile optimizations for ratings */
@media (max-width: 480px) {
  .rating-container {
    padding: 1rem;
    margin: 1rem;
  }

  .star-rating {
    justify-content: center;
  }

  .star {
    font-size: 1.8rem;
  }

  .average-rating {
    flex-direction: column;
    gap: 0.25rem;
  }

  .rating-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .rating-item .rating-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
