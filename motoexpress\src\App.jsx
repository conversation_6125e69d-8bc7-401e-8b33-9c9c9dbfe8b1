import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import Register from './components/Register';
import MotoExpressDashboard from './components/MotoExpressDashboard';
import './App.css';

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('userData');

    if (token && userData) {
      setUser(JSON.parse(userData));
    }
    setLoading(false);
  }, []);

  const handleLogin = (userData) => {
    setUser(userData);
    localStorage.setItem('token', userData.token);
    localStorage.setItem('userData', JSON.stringify(userData));
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Cargando MotoExpress...</p>
      </div>
    );
  }

  return (
    <Router>
      <div className="app">
        <Routes>
          <Route
            path="/login"
            element={
              user ?
                <Navigate to={user.userType === 'driver' ? '/driver' : '/passenger'} /> :
                <Login onLogin={handleLogin} />
            }
          />
          <Route
            path="/register"
            element={
              user ?
                <Navigate to={user.userType === 'driver' ? '/driver' : '/passenger'} /> :
                <Register onLogin={handleLogin} />
            }
          />
          <Route
            path="/dashboard"
            element={
              user ?
                <MotoExpressDashboard user={user} onLogout={handleLogout} /> :
                <Navigate to="/login" />
            }
          />
          <Route
            path="/"
            element={
              user ?
                <Navigate to="/dashboard" /> :
                <Navigate to="/login" />
            }
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
