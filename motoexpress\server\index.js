import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import sqlite3 from 'sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Database setup
const db = new sqlite3.Database(path.join(__dirname, 'motoexpress.db'));

// Initialize database tables
db.serialize(() => {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    phone TEXT,
    user_type TEXT NOT NULL CHECK(user_type IN ('driver', 'passenger')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Drivers table (for driver-specific data)
  db.run(`CREATE TABLE IF NOT EXISTS drivers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE NOT NULL,
    current_balance REAL DEFAULT 0,
    total_earnings REAL DEFAULT 0,
    is_active BOOLEAN DEFAULT 0,
    current_location TEXT,
    last_recharge DATETIME,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
  )`);

  // Recharges table
  db.run(`CREATE TABLE IF NOT EXISTS recharges (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    driver_id INTEGER NOT NULL,
    amount REAL NOT NULL,
    yappy_transaction_id TEXT,
    status TEXT DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (driver_id) REFERENCES drivers (id)
  )`);

  // Trip requests table
  db.run(`CREATE TABLE IF NOT EXISTS trip_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    passenger_id INTEGER NOT NULL,
    pickup_location TEXT NOT NULL,
    destination TEXT NOT NULL,
    initial_price REAL NOT NULL,
    current_price REAL NOT NULL,
    price_increases INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending',
    payment_method TEXT CHECK(payment_method IN ('yappy', 'cash')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (passenger_id) REFERENCES users (id)
  )`);

  // Price negotiations table
  db.run(`CREATE TABLE IF NOT EXISTS price_negotiations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trip_request_id INTEGER NOT NULL,
    driver_id INTEGER NOT NULL,
    offered_price REAL NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_request_id) REFERENCES trip_requests (id),
    FOREIGN KEY (driver_id) REFERENCES drivers (id)
  )`);

  // Trips table (completed trips)
  db.run(`CREATE TABLE IF NOT EXISTS trips (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trip_request_id INTEGER NOT NULL,
    driver_id INTEGER NOT NULL,
    passenger_id INTEGER NOT NULL,
    final_price REAL NOT NULL,
    commission REAL NOT NULL,
    driver_earnings REAL NOT NULL,
    payment_method TEXT NOT NULL,
    status TEXT DEFAULT 'completed',
    started_at DATETIME,
    completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trip_request_id) REFERENCES trip_requests (id),
    FOREIGN KEY (driver_id) REFERENCES drivers (id),
    FOREIGN KEY (passenger_id) REFERENCES users (id)
  )`);

  // Active users table (for real-time tracking)
  db.run(`CREATE TABLE IF NOT EXISTS active_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE NOT NULL,
    user_type TEXT NOT NULL,
    current_location TEXT,
    is_online BOOLEAN DEFAULT 1,
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
  )`);
});

// JWT Secret
const JWT_SECRET = 'motoexpress_secret_key_2024';

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Routes

// Register
app.post('/api/register', async (req, res) => {
  try {
    const { name, email, password, phone, userType } = req.body;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Insert user
    db.run(
      'INSERT INTO users (name, email, password, phone, user_type) VALUES (?, ?, ?, ?, ?)',
      [name, email, hashedPassword, phone, userType],
      function(err) {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed')) {
            return res.status(400).json({ error: 'Email already exists' });
          }
          return res.status(500).json({ error: 'Registration failed' });
        }
        
        const userId = this.lastID;
        
        // If driver, create driver record
        if (userType === 'driver') {
          db.run(
            'INSERT INTO drivers (user_id) VALUES (?)',
            [userId],
            (err) => {
              if (err) {
                return res.status(500).json({ error: 'Driver profile creation failed' });
              }
              
              const token = jwt.sign({ userId, userType }, JWT_SECRET);
              res.json({ token, userId, userType, message: 'Driver registered successfully' });
            }
          );
        } else {
          const token = jwt.sign({ userId, userType }, JWT_SECRET);
          res.json({ token, userId, userType, message: 'Passenger registered successfully' });
        }
      }
    );
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

// Login
app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    db.get(
      'SELECT * FROM users WHERE email = ?',
      [email],
      async (err, user) => {
        if (err) {
          return res.status(500).json({ error: 'Server error' });
        }
        
        if (!user) {
          return res.status(400).json({ error: 'Invalid credentials' });
        }
        
        const validPassword = await bcrypt.compare(password, user.password);
        if (!validPassword) {
          return res.status(400).json({ error: 'Invalid credentials' });
        }
        
        const token = jwt.sign({ userId: user.id, userType: user.user_type }, JWT_SECRET);
        res.json({ 
          token, 
          userId: user.id, 
          userType: user.user_type,
          name: user.name,
          message: 'Login successful' 
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

// Driver routes

// Get driver dashboard data
app.get('/api/driver/dashboard', authenticateToken, (req, res) => {
  if (req.user.userType !== 'driver') {
    return res.status(403).json({ error: 'Access denied' });
  }

  db.get(
    `SELECT d.*, u.name FROM drivers d
     JOIN users u ON d.user_id = u.id
     WHERE d.user_id = ?`,
    [req.user.userId],
    (err, driver) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }

      if (!driver) {
        return res.status(404).json({ error: 'Driver not found' });
      }

      res.json(driver);
    }
  );
});

// Recharge driver account (Yappy simulation)
app.post('/api/driver/recharge', authenticateToken, (req, res) => {
  if (req.user.userType !== 'driver') {
    return res.status(403).json({ error: 'Access denied' });
  }

  const { yappyTransactionId } = req.body;
  const rechargeAmount = 10; // Fixed $10 recharge
  const creditAmount = 100; // $100 credit for platform use

  // Get driver ID
  db.get(
    'SELECT id FROM drivers WHERE user_id = ?',
    [req.user.userId],
    (err, driver) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }

      // Record recharge
      db.run(
        'INSERT INTO recharges (driver_id, amount, yappy_transaction_id, status) VALUES (?, ?, ?, ?)',
        [driver.id, rechargeAmount, yappyTransactionId || 'YAPPY_' + Date.now(), 'completed'],
        function(err) {
          if (err) {
            return res.status(500).json({ error: 'Recharge failed' });
          }

          // Update driver balance and activate
          db.run(
            'UPDATE drivers SET current_balance = ?, is_active = 1, last_recharge = CURRENT_TIMESTAMP WHERE id = ?',
            [creditAmount, driver.id],
            (err) => {
              if (err) {
                return res.status(500).json({ error: 'Balance update failed' });
              }

              res.json({
                message: 'Recharge successful',
                newBalance: creditAmount,
                isActive: true
              });
            }
          );
        }
      );
    }
  );
});

// Trip request routes

// Create trip request (passenger)
app.post('/api/trip/request', authenticateToken, (req, res) => {
  if (req.user.userType !== 'passenger') {
    return res.status(403).json({ error: 'Access denied' });
  }

  const { pickupLocation, destination, initialPrice, paymentMethod } = req.body;

  db.run(
    'INSERT INTO trip_requests (passenger_id, pickup_location, destination, initial_price, current_price, payment_method) VALUES (?, ?, ?, ?, ?, ?)',
    [req.user.userId, pickupLocation, destination, initialPrice, initialPrice, paymentMethod],
    function(err) {
      if (err) {
        return res.status(500).json({ error: 'Trip request failed' });
      }

      const tripRequestId = this.lastID;

      // Emit to all active drivers
      io.emit('new_trip_request', {
        id: tripRequestId,
        pickupLocation,
        destination,
        currentPrice: initialPrice,
        paymentMethod
      });

      res.json({
        message: 'Trip request created',
        tripRequestId,
        status: 'pending'
      });
    }
  );
});

// Get active trip requests (for drivers)
app.get('/api/trip/requests', authenticateToken, (req, res) => {
  if (req.user.userType !== 'driver') {
    return res.status(403).json({ error: 'Access denied' });
  }

  db.all(
    `SELECT tr.*, u.name as passenger_name, u.phone as passenger_phone
     FROM trip_requests tr
     JOIN users u ON tr.passenger_id = u.id
     WHERE tr.status = 'pending'
     ORDER BY tr.created_at DESC`,
    [],
    (err, requests) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }

      res.json(requests);
    }
  );
});

// Increase trip price (passenger - max 3 times)
app.post('/api/trip/:id/increase-price', authenticateToken, (req, res) => {
  if (req.user.userType !== 'passenger') {
    return res.status(403).json({ error: 'Access denied' });
  }

  const { newPrice } = req.body;
  const tripId = req.params.id;

  db.get(
    'SELECT * FROM trip_requests WHERE id = ? AND passenger_id = ? AND status = "pending"',
    [tripId, req.user.userId],
    (err, trip) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }

      if (!trip) {
        return res.status(404).json({ error: 'Trip not found' });
      }

      if (trip.price_increases >= 3) {
        return res.status(400).json({ error: 'Maximum price increases reached' });
      }

      db.run(
        'UPDATE trip_requests SET current_price = ?, price_increases = price_increases + 1 WHERE id = ?',
        [newPrice, tripId],
        (err) => {
          if (err) {
            return res.status(500).json({ error: 'Price update failed' });
          }

          // Emit price update to drivers
          io.emit('trip_price_updated', {
            tripId,
            newPrice,
            priceIncreases: trip.price_increases + 1
          });

          res.json({ message: 'Price updated successfully', newPrice });
        }
      );
    }
  );
});

// Driver counter-offer
app.post('/api/trip/:id/counter-offer', authenticateToken, (req, res) => {
  if (req.user.userType !== 'driver') {
    return res.status(403).json({ error: 'Access denied' });
  }

  const { offeredPrice } = req.body;
  const tripId = req.params.id;

  // Get driver ID
  db.get(
    'SELECT id FROM drivers WHERE user_id = ? AND is_active = 1',
    [req.user.userId],
    (err, driver) => {
      if (err) {
        return res.status(500).json({ error: 'Server error' });
      }

      if (!driver) {
        return res.status(400).json({ error: 'Driver not active or not found' });
      }

      // Check if driver has enough balance
      if (driver.current_balance <= 0) {
        return res.status(400).json({ error: 'Insufficient balance. Please recharge.' });
      }

      db.run(
        'INSERT INTO price_negotiations (trip_request_id, driver_id, offered_price) VALUES (?, ?, ?)',
        [tripId, driver.id, offeredPrice],
        function(err) {
          if (err) {
            return res.status(500).json({ error: 'Counter-offer failed' });
          }

          // Get trip details to emit to passenger
          db.get(
            'SELECT passenger_id FROM trip_requests WHERE id = ?',
            [tripId],
            (err, trip) => {
              if (err) return;

              // Emit counter-offer to passenger
              io.emit('counter_offer_received', {
                tripId,
                driverId: driver.id,
                offeredPrice,
                negotiationId: this.lastID
              });
            }
          );

          res.json({ message: 'Counter-offer sent successfully' });
        }
      );
    }
  );
});

// Accept trip (driver accepts passenger's price or passenger accepts counter-offer)
app.post('/api/trip/:id/accept', authenticateToken, (req, res) => {
  const tripId = req.params.id;
  const { negotiationId } = req.body;

  if (req.user.userType === 'driver') {
    // Driver accepting passenger's original price
    db.get(
      'SELECT id, current_balance FROM drivers WHERE user_id = ? AND is_active = 1',
      [req.user.userId],
      (err, driver) => {
        if (err || !driver) {
          return res.status(400).json({ error: 'Driver not found or inactive' });
        }

        if (driver.current_balance <= 0) {
          return res.status(400).json({ error: 'Insufficient balance. Please recharge.' });
        }

        // Get trip details
        db.get(
          'SELECT * FROM trip_requests WHERE id = ? AND status = "pending"',
          [tripId],
          (err, trip) => {
            if (err || !trip) {
              return res.status(404).json({ error: 'Trip not found' });
            }

            const commission = 1.00; // Fixed $1 commission per trip
            const driverEarnings = trip.current_price; // Driver gets full trip price
            const newBalance = driver.current_balance - commission; // Deduct $1 from balance

            // Create completed trip
            db.run(
              'INSERT INTO trips (trip_request_id, driver_id, passenger_id, final_price, commission, driver_earnings, payment_method) VALUES (?, ?, ?, ?, ?, ?, ?)',
              [tripId, driver.id, trip.passenger_id, trip.current_price, commission, driverEarnings, trip.payment_method],
              (err) => {
                if (err) {
                  return res.status(500).json({ error: 'Trip creation failed' });
                }

                // Update trip request status
                db.run('UPDATE trip_requests SET status = "accepted" WHERE id = ?', [tripId]);

                // Update driver balance and earnings
                db.run(
                  'UPDATE drivers SET current_balance = ?, total_earnings = total_earnings + ?, is_active = ? WHERE id = ?',
                  [newBalance, driverEarnings, newBalance > 0 ? 1 : 0, driver.id],
                  (err) => {
                    if (err) {
                      return res.status(500).json({ error: 'Balance update failed' });
                    }

                    // Emit trip accepted
                    io.emit('trip_accepted', {
                      tripId,
                      driverId: driver.id,
                      finalPrice: trip.current_price,
                      paymentMethod: trip.payment_method
                    });

                    res.json({
                      message: 'Trip accepted successfully',
                      finalPrice: trip.current_price,
                      commission,
                      earnings: driverEarnings,
                      newBalance,
                      isActive: newBalance > 0
                    });
                  }
                );
              }
            );
          }
        );
      }
    );
  }
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 MotoExpress server running on port ${PORT}`);
});
