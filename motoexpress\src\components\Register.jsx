import { useState } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

const API_URL = 'http://localhost:3001/api';

function Register({ onLogin }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    phone: '',
    userType: 'passenger'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API_URL}/register`, formData);
      onLogin(response.data);
    } catch (error) {
      setError(error.response?.data?.error || 'Error al registrarse');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="logo">
          <h1>🏍️ MotoExpress</h1>
          <p>Únete a nuestra plataforma</p>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <h2>Crear Cuenta</h2>
          
          {error && <div className="error-message">{error}</div>}

          <div className="form-group">
            <label htmlFor="name">Nombre Completo</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              placeholder="Tu nombre completo"
            />
          </div>

          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              placeholder="<EMAIL>"
            />
          </div>

          <div className="form-group">
            <label htmlFor="phone">Teléfono</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              required
              placeholder="+507 6000-0000"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Contraseña</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
              placeholder="Mínimo 6 caracteres"
              minLength="6"
            />
          </div>

          <div className="form-group">
            <label htmlFor="userType">Tipo de Usuario</label>
            <select
              id="userType"
              name="userType"
              value={formData.userType}
              onChange={handleChange}
              required
            >
              <option value="passenger">🧑‍💼 Pasajero</option>
              <option value="driver">🏍️ Conductor</option>
            </select>
          </div>

          {formData.userType === 'driver' && (
            <div className="info-box">
              <p>
                <strong>Información para Conductores:</strong><br/>
                • Debes recargar $10 USD para activar la plataforma<br/>
                • Con cada recarga puedes ganar hasta $100 USD<br/>
                • La plataforma cobra 10% de comisión por viaje<br/>
                • Pago de recargas solo por Yappy
              </p>
            </div>
          )}

          <button 
            type="submit" 
            className="btn-primary"
            disabled={loading}
          >
            {loading ? 'Creando cuenta...' : 'Crear Cuenta'}
          </button>

          <div className="auth-links">
            <p>
              ¿Ya tienes cuenta? 
              <Link to="/login"> Inicia sesión aquí</Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Register;
