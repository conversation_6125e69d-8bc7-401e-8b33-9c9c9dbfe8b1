import { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';
import ActiveUsers from './ActiveUsers';

const API_URL = 'http://localhost:3001/api';
const socket = io('http://localhost:3001');

function DriverDashboard({ user, onLogout }) {
  const [activeView, setActiveView] = useState('status'); // 'status', 'passengers', 'trips'
  const [driverData, setDriverData] = useState(null);
  const [tripRequests, setTripRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [recharging, setRecharging] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDriverData();
    fetchTripRequests();

    // Socket listeners
    socket.on('new_trip_request', (tripRequest) => {
      setTripRequests(prev => [tripRequest, ...prev]);
    });

    socket.on('trip_price_updated', (data) => {
      setTripRequests(prev => 
        prev.map(trip => 
          trip.id === data.tripId 
            ? { ...trip, current_price: data.newPrice, price_increases: data.priceIncreases }
            : trip
        )
      );
    });

    return () => {
      socket.off('new_trip_request');
      socket.off('trip_price_updated');
    };
  }, []);

  const fetchDriverData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/driver/dashboard`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setDriverData(response.data);
    } catch (error) {
      setError('Error al cargar datos del conductor');
    } finally {
      setLoading(false);
    }
  };

  const fetchTripRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/trip/requests`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setTripRequests(response.data);
    } catch (error) {
      console.error('Error fetching trip requests:', error);
    }
  };

  const handleRecharge = async () => {
    setRecharging(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/driver/recharge`, {
        yappyTransactionId: `YAPPY_${Date.now()}`
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setDriverData(prev => ({
        ...prev,
        current_balance: response.data.newBalance,
        is_active: response.data.isActive
      }));
      
      alert('¡Recarga exitosa! Tu plataforma está activa.');
    } catch (error) {
      alert('Error en la recarga: ' + (error.response?.data?.error || 'Error desconocido'));
    } finally {
      setRecharging(false);
    }
  };

  const handleCounterOffer = async (tripId, offeredPrice) => {
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/trip/${tripId}/counter-offer`, {
        offeredPrice: parseFloat(offeredPrice)
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      alert('Contraoferta enviada exitosamente');
    } catch (error) {
      alert('Error al enviar contraoferta: ' + (error.response?.data?.error || 'Error desconocido'));
    }
  };

  const handleAcceptTrip = async (tripId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/trip/${tripId}/accept`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // Update driver data
      setDriverData(prev => ({
        ...prev,
        current_balance: response.data.newBalance,
        total_earnings: prev.total_earnings + response.data.earnings,
        is_active: response.data.isActive
      }));
      
      // Remove trip from list
      setTripRequests(prev => prev.filter(trip => trip.id !== tripId));
      
      alert(`¡Viaje aceptado! Ganaste $${response.data.earnings.toFixed(2)}`);
      
      if (!response.data.isActive) {
        alert('Tu saldo se ha agotado. Debes recargar $10 para continuar recibiendo viajes.');
      }
    } catch (error) {
      alert('Error al aceptar viaje: ' + (error.response?.data?.error || 'Error desconocido'));
    }
  };

  if (loading) {
    return <div className="loading">Cargando dashboard...</div>;
  }

  return (
    <div className="dashboard mobile-dashboard">
      <header className="dashboard-header mobile-header">
        <h1>🏍️ MotoExpress</h1>
        <div className="header-actions">
          <span>{user.name}</span>
          <button onClick={onLogout} className="btn-secondary">Salir</button>
        </div>
      </header>

      {/* Mobile Navigation */}
      <div className="mobile-nav">
        <button
          className={`nav-btn ${activeView === 'status' ? 'active' : ''}`}
          onClick={() => setActiveView('status')}
        >
          💰 Estado
        </button>
        <button
          className={`nav-btn ${activeView === 'passengers' ? 'active' : ''}`}
          onClick={() => setActiveView('passengers')}
        >
          👥 Pasajeros
        </button>
        <button
          className={`nav-btn ${activeView === 'trips' ? 'active' : ''}`}
          onClick={() => setActiveView('trips')}
        >
          🚗 Viajes
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="dashboard-content mobile-content">
        {/* Status View */}
        {activeView === 'status' && (
          <div className="status-card">
          <h2>Estado de la Plataforma</h2>
          <div className={`status-indicator ${driverData?.is_active ? 'active' : 'inactive'}`}>
            {driverData?.is_active ? '🟢 ACTIVO' : '🔴 INACTIVO'}
          </div>
          
          <div className="balance-info">
            <div className="balance-item">
              <span>Saldo Actual:</span>
              <strong>${driverData?.current_balance?.toFixed(2) || '0.00'}</strong>
            </div>
            <div className="balance-item">
              <span>Ganancias Totales:</span>
              <strong>${driverData?.total_earnings?.toFixed(2) || '0.00'}</strong>
            </div>
          </div>

          {(!driverData?.is_active || driverData?.current_balance <= 0) && (
            <div className="recharge-section">
              <p className="warning">
                ⚠️ Tu plataforma está inactiva. Recarga $10 USD para activarla y ganar hasta $100 USD.
              </p>
              <button 
                onClick={handleRecharge}
                disabled={recharging}
                className="btn-primary"
              >
                {recharging ? 'Procesando...' : '💳 Recargar con Yappy ($10)'}
              </button>
            </div>
          )}
          </div>
        )}

        {/* Passengers View */}
        {activeView === 'passengers' && (
          <ActiveUsers
            userType="driver"
            onSelectUser={(passenger) => {
              // Handle passenger selection if needed
              console.log('Selected passenger:', passenger);
            }}
          />
        )}

        {/* Trip Requests View */}
        {activeView === 'trips' && (
          <div className="trip-requests">
          <h2>Solicitudes de Viaje ({tripRequests.length})</h2>
          
          {!driverData?.is_active ? (
            <div className="inactive-message">
              <p>Recarga tu cuenta para ver y aceptar solicitudes de viaje.</p>
            </div>
          ) : tripRequests.length === 0 ? (
            <div className="no-trips">
              <p>No hay solicitudes de viaje disponibles en este momento.</p>
            </div>
          ) : (
            <div className="trip-list">
              {tripRequests.map(trip => (
                <TripRequestCard 
                  key={trip.id}
                  trip={trip}
                  onCounterOffer={handleCounterOffer}
                  onAccept={handleAcceptTrip}
                />
              ))}
            </div>
          )}
          </div>
        )}
      </div>
    </div>
  );
}

function TripRequestCard({ trip, onCounterOffer, onAccept }) {
  const [counterPrice, setCounterPrice] = useState('');
  const [showCounterOffer, setShowCounterOffer] = useState(false);

  const handleCounterOfferSubmit = () => {
    if (counterPrice && parseFloat(counterPrice) > trip.current_price) {
      onCounterOffer(trip.id, counterPrice);
      setShowCounterOffer(false);
      setCounterPrice('');
    } else {
      alert('El precio debe ser mayor al precio actual');
    }
  };

  return (
    <div className="trip-card">
      <div className="trip-info">
        <div className="trip-route">
          <p><strong>📍 Origen:</strong> {trip.pickup_location}</p>
          <p><strong>🎯 Destino:</strong> {trip.destination}</p>
        </div>
        
        <div className="trip-details">
          <div className="price-info">
            <span className="current-price">${trip.current_price}</span>
            {trip.price_increases > 0 && (
              <span className="price-increases">
                (Subido {trip.price_increases}/3 veces)
              </span>
            )}
          </div>
          <p><strong>💳 Pago:</strong> {trip.payment_method === 'yappy' ? 'Yappy' : 'Efectivo'}</p>
          <p><strong>👤 Pasajero:</strong> {trip.passenger_name}</p>
        </div>
      </div>

      <div className="trip-actions">
        <button 
          onClick={() => onAccept(trip.id)}
          className="btn-success"
        >
          ✅ Aceptar ${trip.current_price}
        </button>
        
        {!showCounterOffer ? (
          <button 
            onClick={() => setShowCounterOffer(true)}
            className="btn-warning"
          >
            💰 Contraoferta
          </button>
        ) : (
          <div className="counter-offer">
            <input
              type="number"
              step="0.50"
              min={trip.current_price + 0.5}
              value={counterPrice}
              onChange={(e) => setCounterPrice(e.target.value)}
              placeholder="Precio mayor"
            />
            <button onClick={handleCounterOfferSubmit} className="btn-primary">
              Enviar
            </button>
            <button 
              onClick={() => setShowCounterOffer(false)} 
              className="btn-secondary"
            >
              Cancelar
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default DriverDashboard;
