import { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';
import ActiveUsers from './ActiveUsers';
import CameraCapture from './CameraCapture';
import PhotoConfirmation from './PhotoConfirmation';

const API_URL = 'http://localhost:3001/api';
const socket = io('http://localhost:3001');

function MotoExpressDashboard({ user, onLogout }) {
  const [currentMode, setCurrentMode] = useState('passenger'); // 'driver' or 'passenger'
  const [activeView, setActiveView] = useState('users'); // 'users', 'request', 'status'
  
  // Driver states
  const [driverData, setDriverData] = useState(null);
  const [recharging, setRecharging] = useState(false);
  
  // Passenger states
  const [tripForm, setTripForm] = useState({
    pickupLocation: '',
    destination: '',
    paymentMethod: 'yappy'
  });
  const [standardPrice, setStandardPrice] = useState(0);
  const [priceIncrease, setPriceIncrease] = useState(0); // 0, 0.5, 1.0, 1.5
  const [estimatedDistance, setEstimatedDistance] = useState(0);
  const [currentTrip, setCurrentTrip] = useState(null);
  const [counterOffers, setCounterOffers] = useState([]);
  const [selectedDriver, setSelectedDriver] = useState(null);

  // Photo verification states
  const [showCamera, setShowCamera] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [photoVerified, setPhotoVerified] = useState(false);
  
  // Common states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (currentMode === 'driver') {
      fetchDriverData();
    }
    
    // Socket listeners
    socket.emit('join_room', currentMode);
    
    socket.on('counter_offer_received', (data) => {
      if (currentTrip && data.tripId === currentTrip.id) {
        setCounterOffers(prev => [...prev, data]);
      }
    });

    socket.on('trip_accepted', (data) => {
      if (currentTrip && data.tripId === currentTrip.id) {
        alert(`¡Tu viaje fue aceptado! Precio final: $${data.finalPrice}`);
        setCurrentTrip(null);
        setCounterOffers([]);
        resetForm();
      }
    });

    return () => {
      socket.off('counter_offer_received');
      socket.off('trip_accepted');
    };
  }, [currentMode, currentTrip]);

  const fetchDriverData = async () => {
    try {
      const token = localStorage.getItem('token');

      // First try to get driver data
      try {
        const response = await axios.get(`${API_URL}/driver/dashboard`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        setDriverData(response.data);
      } catch (error) {
        // If driver profile doesn't exist, create it
        if (error.response?.status === 403) {
          await axios.post(`${API_URL}/driver/create-profile`, {}, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // Try again to get driver data
          const response = await axios.get(`${API_URL}/driver/dashboard`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          setDriverData(response.data);
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error fetching driver data:', error);
      setDriverData({
        current_balance: 0,
        total_earnings: 0,
        is_active: false
      });
    }
  };

  const handleModeSwitch = (mode) => {
    setCurrentMode(mode);
    setActiveView('users');
    setError('');
    setCurrentTrip(null);
    setCounterOffers([]);
    setSelectedDriver(null);
    resetForm();
  };

  const resetForm = () => {
    setTripForm({
      pickupLocation: '',
      destination: '',
      paymentMethod: 'yappy'
    });
    setStandardPrice(0);
    setPriceIncrease(0);
    setEstimatedDistance(0);
    setCapturedPhoto(null);
    setPhotoVerified(false);
    setShowCamera(false);
  };

  // Calcular precio automáticamente cuando cambien las ubicaciones
  useEffect(() => {
    if (tripForm.pickupLocation && tripForm.destination) {
      const { distance, price } = calculateDistanceAndPrice();
      setEstimatedDistance(distance);
      setStandardPrice(price);
    }
  }, [tripForm.pickupLocation, tripForm.destination]);

  const handleRecharge = async () => {
    setRecharging(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/driver/recharge`, {
        yappyTransactionId: `YAPPY_${Date.now()}`
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setDriverData(prev => ({
        ...prev,
        current_balance: response.data.newBalance,
        is_active: response.data.isActive
      }));
      
      alert('¡Recarga exitosa! Tu plataforma está activa.');
    } catch (error) {
      alert('Error en la recarga: ' + (error.response?.data?.error || 'Error desconocido'));
    } finally {
      setRecharging(false);
    }
  };

  const handleRequestTrip = async (e) => {
    e.preventDefault();

    // Verificar que se haya tomado la foto
    if (!photoVerified) {
      alert('⚠️ Debes verificar tu identidad con una foto antes de solicitar el viaje.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const finalPrice = standardPrice + priceIncrease;

      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/trip/request`, {
        ...tripForm,
        initialPrice: finalPrice,
        estimatedDistance: estimatedDistance,
        standardPrice: standardPrice,
        priceIncrease: priceIncrease,
        verificationPhoto: capturedPhoto, // Enviar la foto de verificación
        preferredDriverId: selectedDriver?.id
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setCurrentTrip({
        id: response.data.tripRequestId,
        ...tripForm,
        currentPrice: finalPrice,
        standardPrice: standardPrice,
        priceIncrease: priceIncrease,
        estimatedDistance: estimatedDistance,
        maxIncreases: 3, // Máximo 3 aumentos de 50 centavos
        status: 'pending',
        selectedDriver
      });

      alert(selectedDriver ?
        `¡Solicitud enviada a ${selectedDriver.name}! Precio: $${finalPrice.toFixed(2)}` :
        `¡Solicitud de viaje enviada! Precio: $${finalPrice.toFixed(2)}`
      );
    } catch (error) {
      setError(error.response?.data?.error || 'Error al solicitar viaje');
    } finally {
      setLoading(false);
    }
  };

  const calculateDistanceAndPrice = () => {
    const pickup = tripForm.pickupLocation.toLowerCase();
    const destination = tripForm.destination.toLowerCase();

    // Coordenadas aproximadas de ubicaciones principales en Ciudad de Panamá
    const locations = {
      'casco viejo': { lat: 8.9536, lng: -79.5340 },
      'casco': { lat: 8.9536, lng: -79.5340 },
      'costa del este': { lat: 9.0581, lng: -79.4656 },
      'costa': { lat: 9.0581, lng: -79.4656 },
      'multiplaza': { lat: 9.0581, lng: -79.4656 },
      'albrook': { lat: 8.9728, lng: -79.5492 },
      'chorrera': { lat: 8.8833, lng: -79.7833 },
      'san miguelito': { lat: 9.0333, lng: -79.4667 },
      'penonome': { lat: 8.5167, lng: -80.3500 },
      'tocumen': { lat: 9.0714, lng: -79.3831 },
      'aeropuerto': { lat: 9.0714, lng: -79.3831 },
      'via españa': { lat: 8.9833, lng: -79.5167 },
      'el dorado': { lat: 8.9833, lng: -79.5167 },
      'condado del rey': { lat: 9.0167, lng: -79.4500 },
      'pueblo nuevo': { lat: 8.9667, lng: -79.5333 },
      'rio abajo': { lat: 8.9833, lng: -79.5000 },
      'betania': { lat: 8.9833, lng: -79.5000 },
      'las cumbres': { lat: 9.0833, lng: -79.5000 },
      'don bosco': { lat: 9.0167, lng: -79.5167 },
      'transistmica': { lat: 9.0500, lng: -79.4000 }
    };

    // Buscar coordenadas del punto de recogida
    let pickupCoords = null;
    let destinationCoords = null;

    for (const [location, coords] of Object.entries(locations)) {
      if (pickup.includes(location)) {
        pickupCoords = coords;
      }
      if (destination.includes(location)) {
        destinationCoords = coords;
      }
    }

    // Si no encuentra ubicaciones específicas, usar coordenadas por defecto del centro de la ciudad
    if (!pickupCoords) pickupCoords = { lat: 8.9833, lng: -79.5167 }; // Centro de Panamá
    if (!destinationCoords) destinationCoords = { lat: 8.9833, lng: -79.5167 };

    // Calcular distancia usando fórmula de Haversine
    const distance = calculateHaversineDistance(
      pickupCoords.lat, pickupCoords.lng,
      destinationCoords.lat, destinationCoords.lng
    );

    // Calcular precio basado en distancia
    const basePrice = 2.00; // Precio base
    const pricePerKm = 0.75; // $0.75 por kilómetro
    const minimumPrice = 3.00; // Precio mínimo

    const calculatedPrice = basePrice + (distance * pricePerKm);
    const finalPrice = Math.max(calculatedPrice, minimumPrice);

    return {
      distance: Math.round(distance * 100) / 100, // Redondear a 2 decimales
      price: Math.round(finalPrice * 100) / 100
    };
  };

  const calculateHaversineDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radio de la Tierra en kilómetros
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return distance;
  };

  const handleSelectUser = (selectedUser) => {
    if (currentMode === 'passenger') {
      setSelectedDriver(selectedUser);
      setActiveView('request');
    }
  };

  const handleFormChange = (e) => {
    setTripForm({
      ...tripForm,
      [e.target.name]: e.target.value
    });
  };

  // Photo verification functions
  const startPhotoVerification = () => {
    setShowCamera(true);
  };

  const handlePhotoCapture = (photoDataUrl) => {
    setCapturedPhoto(photoDataUrl);
    setShowCamera(false);
  };

  const handlePhotoCaptureCancel = () => {
    setShowCamera(false);
  };

  const confirmPhoto = () => {
    setPhotoVerified(true);
    alert('✅ Verificación completada. Ahora puedes solicitar tu viaje.');
  };

  const retakePhoto = () => {
    setCapturedPhoto(null);
    setPhotoVerified(false);
    setShowCamera(true);
  };

  return (
    <div className="dashboard mobile-dashboard">
      {/* Header */}
      <header className="dashboard-header mobile-header">
        <h1>🏍️ MotoExpress</h1>
        <div className="header-actions">
          <span>{user.name}</span>
          <button onClick={onLogout} className="btn-secondary">Salir</button>
        </div>
      </header>

      {/* Mode Switcher */}
      <div className="mode-switcher">
        <button 
          className={`mode-btn ${currentMode === 'passenger' ? 'active' : ''}`}
          onClick={() => handleModeSwitch('passenger')}
        >
          👤 Pasajero
        </button>
        <button 
          className={`mode-btn ${currentMode === 'driver' ? 'active' : ''}`}
          onClick={() => handleModeSwitch('driver')}
        >
          🏍️ Conductor
        </button>
      </div>

      {/* Navigation */}
      <div className="mobile-nav">
        {currentMode === 'passenger' ? (
          <>
            <button 
              className={`nav-btn ${activeView === 'users' ? 'active' : ''}`}
              onClick={() => setActiveView('users')}
            >
              🏍️ Conductores
            </button>
            <button 
              className={`nav-btn ${activeView === 'request' ? 'active' : ''}`}
              onClick={() => setActiveView('request')}
            >
              📍 Solicitar
            </button>
          </>
        ) : (
          <>
            <button 
              className={`nav-btn ${activeView === 'status' ? 'active' : ''}`}
              onClick={() => setActiveView('status')}
            >
              💰 Estado
            </button>
            <button 
              className={`nav-btn ${activeView === 'users' ? 'active' : ''}`}
              onClick={() => setActiveView('users')}
            >
              👥 Pasajeros
            </button>
          </>
        )}
      </div>

      {error && <div className="error-message">{error}</div>}

      {/* Content */}
      <div className="dashboard-content mobile-content">
        {currentMode === 'passenger' ? (
          // PASSENGER MODE
          !currentTrip ? (
            <>
              {activeView === 'users' && (
                <ActiveUsers 
                  userType="passenger" 
                  onSelectUser={handleSelectUser}
                />
              )}

              {activeView === 'request' && (
                <div className="trip-request-form">
                  {selectedDriver && (
                    <div className="selected-driver">
                      <h3>Conductor Seleccionado:</h3>
                      <div className="driver-info">
                        <p><strong>🏍️ {selectedDriver.name}</strong></p>
                        <p>📞 {selectedDriver.phone}</p>
                        <button 
                          onClick={() => setActiveView('users')} 
                          className="btn-secondary"
                        >
                          Cambiar
                        </button>
                      </div>
                    </div>
                  )}
                  
                  <h2>Solicitar Viaje</h2>
                  
                  <form onSubmit={handleRequestTrip}>
                    <div className="form-group">
                      <label>📍 Punto de Recogida</label>
                      <input
                        type="text"
                        name="pickupLocation"
                        value={tripForm.pickupLocation}
                        onChange={handleFormChange}
                        required
                        placeholder="Ej: Casco Viejo, Plaza Francia"
                      />
                    </div>

                    <div className="form-group">
                      <label>🎯 Destino</label>
                      <input
                        type="text"
                        name="destination"
                        value={tripForm.destination}
                        onChange={handleFormChange}
                        required
                        placeholder="Ej: Costa del Este, Multiplaza"
                      />
                    </div>

                    <div className="form-group">
                      <label>💳 Método de Pago</label>
                      <select
                        name="paymentMethod"
                        value={tripForm.paymentMethod}
                        onChange={handleFormChange}
                        required
                      >
                        <option value="yappy">Yappy</option>
                        <option value="cash">Efectivo</option>
                      </select>
                    </div>

                    {/* Información de distancia y precio */}
                    {estimatedDistance > 0 && (
                      <div className="trip-calculation">
                        <div className="distance-info">
                          <span>📏 Distancia estimada: <strong>{estimatedDistance} km</strong></span>
                        </div>

                        <div className="price-calculator">
                          <div className="standard-price">
                            <span>💰 Precio estándar: <strong>${standardPrice.toFixed(2)}</strong></span>
                          </div>

                          <div className="price-increase-section">
                            <label>Aumentar precio (opcional):</label>
                            <div className="price-buttons">
                              <button
                                type="button"
                                className={`price-btn ${priceIncrease === 0 ? 'active' : ''}`}
                                onClick={() => setPriceIncrease(0)}
                              >
                                +$0.00
                              </button>
                              <button
                                type="button"
                                className={`price-btn ${priceIncrease === 0.5 ? 'active' : ''}`}
                                onClick={() => setPriceIncrease(0.5)}
                              >
                                +$0.50
                              </button>
                              <button
                                type="button"
                                className={`price-btn ${priceIncrease === 1.0 ? 'active' : ''}`}
                                onClick={() => setPriceIncrease(1.0)}
                              >
                                +$1.00
                              </button>
                              <button
                                type="button"
                                className={`price-btn ${priceIncrease === 1.5 ? 'active' : ''}`}
                                onClick={() => setPriceIncrease(1.5)}
                              >
                                +$1.50
                              </button>
                            </div>
                          </div>

                          <div className="final-price">
                            <span>🎯 Precio final: <strong>${(standardPrice + priceIncrease).toFixed(2)}</strong></span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Verificación de Foto */}
                    <div className="photo-verification-section">
                      <h4>📸 Verificación de Identidad</h4>
                      <p>Por seguridad, debes verificar tu identidad antes de cada viaje</p>

                      {!photoVerified ? (
                        <div className="photo-verification-pending">
                          {!capturedPhoto ? (
                            <button
                              type="button"
                              onClick={startPhotoVerification}
                              className="btn-warning photo-btn"
                            >
                              📸 Tomar Foto de Verificación
                            </button>
                          ) : (
                            <div className="photo-preview-small">
                              <img src={capturedPhoto} alt="Foto capturada" />
                              <div className="photo-actions">
                                <button
                                  type="button"
                                  onClick={confirmPhoto}
                                  className="btn-success"
                                >
                                  ✅ Confirmar
                                </button>
                                <button
                                  type="button"
                                  onClick={retakePhoto}
                                  className="btn-secondary"
                                >
                                  🔄 Repetir
                                </button>
                              </div>
                            </div>
                          )}
                          <p className="verification-warning">
                            ⚠️ Debes completar la verificación para continuar
                          </p>
                        </div>
                      ) : (
                        <div className="photo-verification-complete">
                          <div className="verification-success">
                            <span className="success-icon">✅</span>
                            <span>Identidad verificada correctamente</span>
                          </div>
                          <button
                            type="button"
                            onClick={retakePhoto}
                            className="btn-secondary small-btn"
                          >
                            🔄 Tomar nueva foto
                          </button>
                        </div>
                      )}
                    </div>

                    <button
                      type="submit"
                      className={`btn-primary ${!photoVerified ? 'disabled' : ''}`}
                      disabled={loading || !photoVerified}
                    >
                      {loading ? 'Solicitando...' :
                       !photoVerified ? '🔒 Completa la verificación' :
                       '🏍️ Solicitar Viaje'}
                    </button>
                  </form>
                </div>
              )}
            </>
          ) : (
            <div className="active-trip">
              <h2>Viaje Solicitado</h2>
              <p>Precio: ${currentTrip.currentPrice}</p>
              <button 
                onClick={() => setCurrentTrip(null)}
                className="btn-danger"
              >
                Cancelar Viaje
              </button>
            </div>
          )
        ) : (
          // DRIVER MODE
          <>
            {activeView === 'status' && (
              <div className="status-card">
                <h2>Estado de la Plataforma</h2>
                <div className={`status-indicator ${driverData?.is_active ? 'active' : 'inactive'}`}>
                  {driverData?.is_active ? '🟢 ACTIVO' : '🔴 INACTIVO'}
                </div>
                
                <div className="balance-info">
                  <div className="balance-item">
                    <span>Saldo:</span>
                    <strong>${driverData?.current_balance?.toFixed(2) || '0.00'}</strong>
                  </div>
                  <div className="balance-item">
                    <span>Ganancias:</span>
                    <strong>${driverData?.total_earnings?.toFixed(2) || '0.00'}</strong>
                  </div>
                </div>

                {(!driverData?.is_active || driverData?.current_balance <= 0) && (
                  <div className="recharge-section">
                    <p className="warning">
                      ⚠️ Recarga $10 para activar la plataforma
                    </p>
                    <button 
                      onClick={handleRecharge}
                      disabled={recharging}
                      className="btn-primary"
                    >
                      {recharging ? 'Procesando...' : '💳 Recargar $10'}
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeView === 'users' && (
              <ActiveUsers 
                userType="driver" 
                onSelectUser={handleSelectUser}
              />
            )}
          </>
        )}
      </div>

      {/* Camera Capture Modal */}
      {showCamera && (
        <CameraCapture
          onCapture={handlePhotoCapture}
          onCancel={handlePhotoCaptureCancel}
        />
      )}

      {/* Photo Confirmation Modal */}
      {capturedPhoto && !photoVerified && !showCamera && (
        <PhotoConfirmation
          photo={capturedPhoto}
          onConfirm={confirmPhoto}
          onRetake={retakePhoto}
        />
      )}
    </div>
  );
}

export default MotoExpressDashboard;
