import { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';
import ActiveUsers from './ActiveUsers';

const API_URL = 'http://localhost:3001/api';
const socket = io('http://localhost:3001');

function MotoExpressDashboard({ user, onLogout }) {
  const [currentMode, setCurrentMode] = useState('passenger'); // 'driver' or 'passenger'
  const [activeView, setActiveView] = useState('users'); // 'users', 'request', 'status'
  
  // Driver states
  const [driverData, setDriverData] = useState(null);
  const [recharging, setRecharging] = useState(false);
  
  // Passenger states
  const [tripForm, setTripForm] = useState({
    pickupLocation: '',
    destination: '',
    initialPrice: '',
    paymentMethod: 'yappy'
  });
  const [currentTrip, setCurrentTrip] = useState(null);
  const [counterOffers, setCounterOffers] = useState([]);
  const [selectedDriver, setSelectedDriver] = useState(null);
  
  // Common states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (currentMode === 'driver') {
      fetchDriverData();
    }
    
    // Socket listeners
    socket.emit('join_room', currentMode);
    
    socket.on('counter_offer_received', (data) => {
      if (currentTrip && data.tripId === currentTrip.id) {
        setCounterOffers(prev => [...prev, data]);
      }
    });

    socket.on('trip_accepted', (data) => {
      if (currentTrip && data.tripId === currentTrip.id) {
        alert(`¡Tu viaje fue aceptado! Precio final: $${data.finalPrice}`);
        setCurrentTrip(null);
        setCounterOffers([]);
        resetForm();
      }
    });

    return () => {
      socket.off('counter_offer_received');
      socket.off('trip_accepted');
    };
  }, [currentMode, currentTrip]);

  const fetchDriverData = async () => {
    try {
      const token = localStorage.getItem('token');

      // First try to get driver data
      try {
        const response = await axios.get(`${API_URL}/driver/dashboard`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        setDriverData(response.data);
      } catch (error) {
        // If driver profile doesn't exist, create it
        if (error.response?.status === 403) {
          await axios.post(`${API_URL}/driver/create-profile`, {}, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // Try again to get driver data
          const response = await axios.get(`${API_URL}/driver/dashboard`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          setDriverData(response.data);
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error fetching driver data:', error);
      setDriverData({
        current_balance: 0,
        total_earnings: 0,
        is_active: false
      });
    }
  };

  const handleModeSwitch = (mode) => {
    setCurrentMode(mode);
    setActiveView('users');
    setError('');
    setCurrentTrip(null);
    setCounterOffers([]);
    setSelectedDriver(null);
    resetForm();
  };

  const resetForm = () => {
    setTripForm({
      pickupLocation: '',
      destination: '',
      initialPrice: '',
      paymentMethod: 'yappy'
    });
  };

  const handleRecharge = async () => {
    setRecharging(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/driver/recharge`, {
        yappyTransactionId: `YAPPY_${Date.now()}`
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setDriverData(prev => ({
        ...prev,
        current_balance: response.data.newBalance,
        is_active: response.data.isActive
      }));
      
      alert('¡Recarga exitosa! Tu plataforma está activa.');
    } catch (error) {
      alert('Error en la recarga: ' + (error.response?.data?.error || 'Error desconocido'));
    } finally {
      setRecharging(false);
    }
  };

  const handleRequestTrip = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const standardPrice = calculateStandardPrice();
      const finalPrice = tripForm.initialPrice || standardPrice;

      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/trip/request`, {
        ...tripForm,
        initialPrice: parseFloat(finalPrice),
        preferredDriverId: selectedDriver?.id
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setCurrentTrip({
        id: response.data.tripRequestId,
        ...tripForm,
        currentPrice: parseFloat(finalPrice),
        priceIncreases: 0,
        status: 'pending',
        selectedDriver
      });

      alert(selectedDriver ? 
        `¡Solicitud enviada a ${selectedDriver.name}!` : 
        '¡Solicitud de viaje enviada!'
      );
    } catch (error) {
      setError(error.response?.data?.error || 'Error al solicitar viaje');
    } finally {
      setLoading(false);
    }
  };

  const calculateStandardPrice = () => {
    const pickup = tripForm.pickupLocation.toLowerCase();
    const destination = tripForm.destination.toLowerCase();
    
    let basePrice = 3.00;
    if (pickup.includes('casco') || destination.includes('casco')) basePrice += 2.00;
    if (pickup.includes('costa') || destination.includes('costa')) basePrice += 3.00;
    if (pickup.includes('chorrera') || destination.includes('chorrera')) basePrice += 5.00;
    if (pickup.includes('san miguelito') || destination.includes('san miguelito')) basePrice += 2.50;
    
    return basePrice;
  };

  const handleSelectUser = (selectedUser) => {
    if (currentMode === 'passenger') {
      setSelectedDriver(selectedUser);
      setActiveView('request');
    }
  };

  const handleFormChange = (e) => {
    setTripForm({
      ...tripForm,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="dashboard mobile-dashboard">
      {/* Header */}
      <header className="dashboard-header mobile-header">
        <h1>🏍️ MotoExpress</h1>
        <div className="header-actions">
          <span>{user.name}</span>
          <button onClick={onLogout} className="btn-secondary">Salir</button>
        </div>
      </header>

      {/* Mode Switcher */}
      <div className="mode-switcher">
        <button 
          className={`mode-btn ${currentMode === 'passenger' ? 'active' : ''}`}
          onClick={() => handleModeSwitch('passenger')}
        >
          👤 Pasajero
        </button>
        <button 
          className={`mode-btn ${currentMode === 'driver' ? 'active' : ''}`}
          onClick={() => handleModeSwitch('driver')}
        >
          🏍️ Conductor
        </button>
      </div>

      {/* Navigation */}
      <div className="mobile-nav">
        {currentMode === 'passenger' ? (
          <>
            <button 
              className={`nav-btn ${activeView === 'users' ? 'active' : ''}`}
              onClick={() => setActiveView('users')}
            >
              🏍️ Conductores
            </button>
            <button 
              className={`nav-btn ${activeView === 'request' ? 'active' : ''}`}
              onClick={() => setActiveView('request')}
            >
              📍 Solicitar
            </button>
          </>
        ) : (
          <>
            <button 
              className={`nav-btn ${activeView === 'status' ? 'active' : ''}`}
              onClick={() => setActiveView('status')}
            >
              💰 Estado
            </button>
            <button 
              className={`nav-btn ${activeView === 'users' ? 'active' : ''}`}
              onClick={() => setActiveView('users')}
            >
              👥 Pasajeros
            </button>
          </>
        )}
      </div>

      {error && <div className="error-message">{error}</div>}

      {/* Content */}
      <div className="dashboard-content mobile-content">
        {currentMode === 'passenger' ? (
          // PASSENGER MODE
          !currentTrip ? (
            <>
              {activeView === 'users' && (
                <ActiveUsers 
                  userType="passenger" 
                  onSelectUser={handleSelectUser}
                />
              )}

              {activeView === 'request' && (
                <div className="trip-request-form">
                  {selectedDriver && (
                    <div className="selected-driver">
                      <h3>Conductor Seleccionado:</h3>
                      <div className="driver-info">
                        <p><strong>🏍️ {selectedDriver.name}</strong></p>
                        <p>📞 {selectedDriver.phone}</p>
                        <button 
                          onClick={() => setActiveView('users')} 
                          className="btn-secondary"
                        >
                          Cambiar
                        </button>
                      </div>
                    </div>
                  )}
                  
                  <h2>Solicitar Viaje</h2>
                  
                  <form onSubmit={handleRequestTrip}>
                    <div className="form-group">
                      <label>📍 Punto de Recogida</label>
                      <input
                        type="text"
                        name="pickupLocation"
                        value={tripForm.pickupLocation}
                        onChange={handleFormChange}
                        required
                        placeholder="Ej: Casco Viejo, Plaza Francia"
                      />
                    </div>

                    <div className="form-group">
                      <label>🎯 Destino</label>
                      <input
                        type="text"
                        name="destination"
                        value={tripForm.destination}
                        onChange={handleFormChange}
                        required
                        placeholder="Ej: Costa del Este, Multiplaza"
                      />
                    </div>

                    <div className="form-group">
                      <label>💳 Método de Pago</label>
                      <select
                        name="paymentMethod"
                        value={tripForm.paymentMethod}
                        onChange={handleFormChange}
                        required
                      >
                        <option value="yappy">Yappy</option>
                        <option value="cash">Efectivo</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label>💰 Precio Ofrecido (Opcional)</label>
                      <input
                        type="number"
                        step="0.50"
                        min="2.00"
                        name="initialPrice"
                        value={tripForm.initialPrice}
                        onChange={handleFormChange}
                        placeholder={`Estándar: $${calculateStandardPrice().toFixed(2)}`}
                      />
                    </div>

                    <button 
                      type="submit" 
                      className="btn-primary"
                      disabled={loading}
                    >
                      {loading ? 'Solicitando...' : '🏍️ Solicitar Viaje'}
                    </button>
                  </form>
                </div>
              )}
            </>
          ) : (
            <div className="active-trip">
              <h2>Viaje Solicitado</h2>
              <p>Precio: ${currentTrip.currentPrice}</p>
              <button 
                onClick={() => setCurrentTrip(null)}
                className="btn-danger"
              >
                Cancelar Viaje
              </button>
            </div>
          )
        ) : (
          // DRIVER MODE
          <>
            {activeView === 'status' && (
              <div className="status-card">
                <h2>Estado de la Plataforma</h2>
                <div className={`status-indicator ${driverData?.is_active ? 'active' : 'inactive'}`}>
                  {driverData?.is_active ? '🟢 ACTIVO' : '🔴 INACTIVO'}
                </div>
                
                <div className="balance-info">
                  <div className="balance-item">
                    <span>Saldo:</span>
                    <strong>${driverData?.current_balance?.toFixed(2) || '0.00'}</strong>
                  </div>
                  <div className="balance-item">
                    <span>Ganancias:</span>
                    <strong>${driverData?.total_earnings?.toFixed(2) || '0.00'}</strong>
                  </div>
                </div>

                {(!driverData?.is_active || driverData?.current_balance <= 0) && (
                  <div className="recharge-section">
                    <p className="warning">
                      ⚠️ Recarga $10 para activar la plataforma
                    </p>
                    <button 
                      onClick={handleRecharge}
                      disabled={recharging}
                      className="btn-primary"
                    >
                      {recharging ? 'Procesando...' : '💳 Recargar $10'}
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeView === 'users' && (
              <ActiveUsers 
                userType="driver" 
                onSelectUser={handleSelectUser}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default MotoExpressDashboard;
