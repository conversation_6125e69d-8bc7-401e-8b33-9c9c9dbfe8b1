import { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';

const API_URL = 'http://localhost:3001/api';
const socket = io('http://localhost:3001');

function PassengerDashboard({ user, onLogout }) {
  const [tripForm, setTripForm] = useState({
    pickupLocation: '',
    destination: '',
    initialPrice: '',
    paymentMethod: 'yappy'
  });
  const [currentTrip, setCurrentTrip] = useState(null);
  const [counterOffers, setCounterOffers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // Socket listeners
    socket.on('counter_offer_received', (data) => {
      if (currentTrip && data.tripId === currentTrip.id) {
        setCounterOffers(prev => [...prev, data]);
      }
    });

    socket.on('trip_accepted', (data) => {
      if (currentTrip && data.tripId === currentTrip.id) {
        alert(`¡Tu viaje fue aceptado! Precio final: $${data.finalPrice}`);
        setCurrentTrip(null);
        setCounterOffers([]);
        resetForm();
      }
    });

    return () => {
      socket.off('counter_offer_received');
      socket.off('trip_accepted');
    };
  }, [currentTrip]);

  const resetForm = () => {
    setTripForm({
      pickupLocation: '',
      destination: '',
      initialPrice: '',
      paymentMethod: 'yappy'
    });
  };

  const handleFormChange = (e) => {
    setTripForm({
      ...tripForm,
      [e.target.name]: e.target.value
    });
  };

  const calculateStandardPrice = () => {
    // Simple price calculation based on distance estimation
    const pickup = tripForm.pickupLocation.toLowerCase();
    const destination = tripForm.destination.toLowerCase();
    
    // Basic price calculation (this would be more sophisticated in a real app)
    let basePrice = 3.00; // Base fare
    
    // Add distance estimation based on common locations
    if (pickup.includes('casco') || destination.includes('casco')) basePrice += 2.00;
    if (pickup.includes('costa') || destination.includes('costa')) basePrice += 3.00;
    if (pickup.includes('chorrera') || destination.includes('chorrera')) basePrice += 5.00;
    if (pickup.includes('san miguelito') || destination.includes('san miguelito')) basePrice += 2.50;
    
    return basePrice;
  };

  const handleRequestTrip = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const standardPrice = calculateStandardPrice();
      const finalPrice = tripForm.initialPrice || standardPrice;

      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/trip/request`, {
        ...tripForm,
        initialPrice: parseFloat(finalPrice)
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setCurrentTrip({
        id: response.data.tripRequestId,
        ...tripForm,
        currentPrice: parseFloat(finalPrice),
        priceIncreases: 0,
        status: 'pending'
      });

      alert('¡Solicitud de viaje enviada! Esperando respuesta de conductores...');
    } catch (error) {
      setError(error.response?.data?.error || 'Error al solicitar viaje');
    } finally {
      setLoading(false);
    }
  };

  const handleIncreasePrice = async () => {
    if (!currentTrip || currentTrip.priceIncreases >= 3) return;

    const newPrice = currentTrip.currentPrice + 1.00; // Increase by $1

    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/trip/${currentTrip.id}/increase-price`, {
        newPrice
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setCurrentTrip(prev => ({
        ...prev,
        currentPrice: newPrice,
        priceIncreases: prev.priceIncreases + 1
      }));

      alert(`Precio aumentado a $${newPrice.toFixed(2)}`);
    } catch (error) {
      alert('Error al aumentar precio: ' + (error.response?.data?.error || 'Error desconocido'));
    }
  };

  const handleAcceptCounterOffer = async (negotiationId, offeredPrice) => {
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/trip/${currentTrip.id}/accept`, {
        negotiationId
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      alert(`¡Contraoferta aceptada! Precio final: $${offeredPrice}`);
      setCurrentTrip(null);
      setCounterOffers([]);
      resetForm();
    } catch (error) {
      alert('Error al aceptar contraoferta: ' + (error.response?.data?.error || 'Error desconocido'));
    }
  };

  const handleCancelTrip = () => {
    setCurrentTrip(null);
    setCounterOffers([]);
    resetForm();
  };

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>🏍️ MotoExpress - Pasajero</h1>
        <div className="header-actions">
          <span>Hola, {user.name}</span>
          <button onClick={onLogout} className="btn-secondary">Cerrar Sesión</button>
        </div>
      </header>

      {error && <div className="error-message">{error}</div>}

      <div className="dashboard-content">
        {!currentTrip ? (
          /* Trip Request Form */
          <div className="trip-request-form">
            <h2>Solicitar Viaje</h2>
            
            <form onSubmit={handleRequestTrip}>
              <div className="form-group">
                <label htmlFor="pickupLocation">📍 Punto de Recogida</label>
                <input
                  type="text"
                  id="pickupLocation"
                  name="pickupLocation"
                  value={tripForm.pickupLocation}
                  onChange={handleFormChange}
                  required
                  placeholder="Ej: Casco Viejo, Plaza Francia"
                />
              </div>

              <div className="form-group">
                <label htmlFor="destination">🎯 Destino</label>
                <input
                  type="text"
                  id="destination"
                  name="destination"
                  value={tripForm.destination}
                  onChange={handleFormChange}
                  required
                  placeholder="Ej: Costa del Este, Multiplaza"
                />
              </div>

              <div className="form-group">
                <label htmlFor="paymentMethod">💳 Método de Pago</label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={tripForm.paymentMethod}
                  onChange={handleFormChange}
                  required
                >
                  <option value="yappy">Yappy</option>
                  <option value="cash">Efectivo</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="initialPrice">💰 Precio Ofrecido (Opcional)</label>
                <input
                  type="number"
                  step="0.50"
                  min="2.00"
                  id="initialPrice"
                  name="initialPrice"
                  value={tripForm.initialPrice}
                  onChange={handleFormChange}
                  placeholder={`Precio estándar: $${calculateStandardPrice().toFixed(2)}`}
                />
                <small>Deja vacío para usar precio estándar</small>
              </div>

              <button 
                type="submit" 
                className="btn-primary"
                disabled={loading}
              >
                {loading ? 'Solicitando...' : '🏍️ Solicitar Viaje'}
              </button>
            </form>
          </div>
        ) : (
          /* Active Trip */
          <div className="active-trip">
            <h2>Viaje Solicitado</h2>
            
            <div className="trip-details">
              <div className="route-info">
                <p><strong>📍 Origen:</strong> {currentTrip.pickupLocation}</p>
                <p><strong>🎯 Destino:</strong> {currentTrip.destination}</p>
                <p><strong>💳 Pago:</strong> {currentTrip.paymentMethod === 'yappy' ? 'Yappy' : 'Efectivo'}</p>
              </div>

              <div className="price-section">
                <div className="current-price">
                  <span>Precio Actual: </span>
                  <strong>${currentTrip.currentPrice.toFixed(2)}</strong>
                </div>
                
                {currentTrip.priceIncreases < 3 && (
                  <div className="price-actions">
                    <p>Puedes subir el precio {3 - currentTrip.priceIncreases} vez(es) más</p>
                    <button 
                      onClick={handleIncreasePrice}
                      className="btn-warning"
                    >
                      ⬆️ Subir Precio (+$1.00)
                    </button>
                  </div>
                )}
                
                {currentTrip.priceIncreases >= 3 && (
                  <p className="max-increases">Has alcanzado el máximo de aumentos de precio</p>
                )}
              </div>
            </div>

            {/* Counter Offers */}
            {counterOffers.length > 0 && (
              <div className="counter-offers">
                <h3>Contraofertas de Conductores</h3>
                {counterOffers.map((offer, index) => (
                  <div key={index} className="counter-offer-card">
                    <div className="offer-info">
                      <span className="offer-price">${offer.offeredPrice}</span>
                      <span className="offer-driver">Conductor #{offer.driverId}</span>
                    </div>
                    <button 
                      onClick={() => handleAcceptCounterOffer(offer.negotiationId, offer.offeredPrice)}
                      className="btn-success"
                    >
                      ✅ Aceptar
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className="trip-actions">
              <button 
                onClick={handleCancelTrip}
                className="btn-danger"
              >
                ❌ Cancelar Viaje
              </button>
            </div>

            <div className="waiting-message">
              <p>🔄 Esperando respuesta de conductores...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default PassengerDashboard;
